`timescale 1ns / 1ps
`include "defines.vh"

// Behavioral model for IROM IP core
// Complete test program for 21 mandatory RISC-V instructions
module IROM (
    input  wire [13:0] a,    // Address input (14-bit for normal mode)
    output reg  [31:0] spo   // Data output
);

    // Complete test program for 21 mandatory RISC-V instructions
    always @(*) begin
        case (a)
            // ========== 算术运算指令测试 (5条) ==========
            // 1. ADDI - 立即数加法
            14'h0000: spo = 32'h00100093; // addi x1, x0, 1      # x1 = 0 + 1 = 1
            14'h0001: spo = 32'h00200113; // addi x2, x0, 2      # x2 = 0 + 2 = 2
            14'h0002: spo = 32'h00300193; // addi x3, x0, 3      # x3 = 0 + 3 = 3
            
            // 2. ADD - 加法
            14'h0003: spo = 32'h002081b3; // add  x3, x1, x2     # x3 = x1 + x2 = 1 + 2 = 3
            14'h0004: spo = 32'h00308233; // add  x4, x1, x3     # x4 = x1 + x3 = 1 + 3 = 4
            
            // 3. SUB - 减法
            14'h0005: spo = 32'h402082b3; // sub  x5, x1, x2     # x5 = x1 - x2 = 1 - 2 = -1
            14'h0006: spo = 32'h40110333; // sub  x6, x2, x1     # x6 = x2 - x1 = 2 - 1 = 1
            
            // 4. LUI - 加载上位立即数
            14'h0007: spo = 32'h123453b7; // lui  x7, 0x12345    # x7 = 0x12345000
            14'h0008: spo = 32'habcde437; // lui  x8, 0xabcde    # x8 = 0xabcde000
            
            // 5. AUIPC - PC加上位立即数
            14'h0009: spo = 32'h123454b7; // auipc x9, 0x12345   # x9 = PC + 0x12345000
            14'h000A: spo = 32'h56789537; // auipc x10, 0x56789  # x10 = PC + 0x56789000
            
            // ========== 逻辑运算指令测试 (6条) ==========
            // 6. AND - 逻辑与
            14'h000B: spo = 32'h0020f5b3; // and  x11, x1, x2    # x11 = x1 & x2 = 1 & 2 = 0
            14'h000C: spo = 32'h0030f633; // and  x12, x1, x3    # x12 = x1 & x3 = 1 & 3 = 1
            
            // 7. ANDI - 立即数逻辑与
            14'h000D: spo = 32'h00f0f693; // andi x13, x1, 15    # x13 = x1 & 15 = 1 & 15 = 1
            14'h000E: spo = 32'h0ff17713; // andi x14, x2, 255   # x14 = x2 & 255 = 2 & 255 = 2
            
            // 8. OR - 逻辑或
            14'h000F: spo = 32'h0020e7b3; // or   x15, x1, x2    # x15 = x1 | x2 = 1 | 2 = 3
            14'h0010: spo = 32'h0030e833; // or   x16, x1, x3    # x16 = x1 | x3 = 1 | 3 = 3
            
            // 9. ORI - 立即数逻辑或
            14'h0011: spo = 32'h00f0e8b3; // ori  x17, x1, 15    # x17 = x1 | 15 = 1 | 15 = 15
            14'h0012: spo = 32'h0ff16933; // ori  x18, x2, 255   # x18 = x2 | 255 = 2 | 255 = 255
            
            // 10. XOR - 逻辑异或
            14'h0013: spo = 32'h0020c9b3; // xor  x19, x1, x2    # x19 = x1 ^ x2 = 1 ^ 2 = 3
            14'h0014: spo = 32'h0030ca33; // xor  x20, x1, x3    # x20 = x1 ^ x3 = 1 ^ 3 = 2
            
            // 11. XORI - 立即数逻辑异或
            14'h0015: spo = 32'h00f0cab3; // xori x21, x1, 15    # x21 = x1 ^ 15 = 1 ^ 15 = 14
            14'h0016: spo = 32'h0ff14b33; // xori x22, x2, 255   # x22 = x2 ^ 255 = 2 ^ 255 = 253
            
            // ========== 移位运算指令测试 (6条) ==========
            // 12. SLL - 逻辑左移
            14'h0017: spo = 32'h00209bb3; // sll  x23, x1, x2    # x23 = x1 << x2 = 1 << 2 = 4
            14'h0018: spo = 32'h00311c33; // sll  x24, x2, x3    # x24 = x2 << x3 = 2 << 3 = 16
            
            // 13. SLLI - 立即数逻辑左移
            14'h0019: spo = 32'h00209cb3; // slli x25, x1, 2     # x25 = x1 << 2 = 1 << 2 = 4
            14'h001A: spo = 32'h00311d33; // slli x26, x2, 3     # x26 = x2 << 3 = 2 << 3 = 16
            
            // 14. SRL - 逻辑右移
            14'h001B: spo = 32'h0020ddb3; // srl  x27, x1, x2    # x27 = x1 >> x2 = 1 >> 2 = 0
            14'h001C: spo = 32'h00315e33; // srl  x28, x2, x3    # x28 = x2 >> x3 = 2 >> 3 = 0
            
            // 15. SRLI - 立即数逻辑右移
            14'h001D: spo = 32'h0020deb3; // srli x29, x1, 2     # x29 = x1 >> 2 = 1 >> 2 = 0
            14'h001E: spo = 32'h00315f33; // srli x30, x2, 3     # x30 = x2 >> 3 = 2 >> 3 = 0
            
            // 16. SRA - 算术右移
            14'h001F: spo = 32'h4020dfb3; // sra  x31, x1, x2    # x31 = x1 >>> x2 = 1 >>> 2 = 0
            
            // 17. SRAI - 立即数算术右移
            14'h0020: spo = 32'h4020d093; // srai x1, x1, 2     # x1 = x1 >>> 2 = 1 >>> 2 = 0
            
            // ========== 比较指令测试 (2条) ==========
            // 18. SLT - 有符号比较小于
            14'h0021: spo = 32'h0020a133; // slt  x2, x1, x2     # x2 = (x1 < x2) ? 1 : 0
            14'h0022: spo = 32'h0030a1b3; // slt  x3, x1, x3     # x3 = (x1 < x3) ? 1 : 0
            
            // 19. SLTI - 立即数有符号比较小于
            14'h0023: spo = 32'h00a0a233; // slti x4, x1, 10     # x4 = (x1 < 10) ? 1 : 0
            14'h0024: spo = 32'hfff0a2b3; // slti x5, x1, -1     # x5 = (x1 < -1) ? 1 : 0
            
            // ========== 跳转链接指令测试 (2条) ==========
            // 20. JAL - 跳转并链接
            14'h0025: spo = 32'h008000ef; // jal  x1, 8          # x1 = PC+4, PC = PC+8
            14'h0026: spo = 32'h00000013; // nop                 # 被跳过
            14'h0027: spo = 32'h00000013; // nop                 # 被跳过
            14'h0028: spo = 32'h00c00167; // jalr x2, x1, 12     # x2 = PC+4, PC = x1+12
            
            // 21. JALR - 寄存器跳转并链接
            14'h0029: spo = 32'h00008067; // jalr x0, x1, 0      # PC = x1 + 0
            
            // ========== 综合测试程序 ==========
            14'h002A: spo = 32'h00100093; // addi x1, x0, 1      # 重新开始测试
            14'h002B: spo = 32'h00200113; // addi x2, x0, 2      # x2 = 2
            14'h002C: spo = 32'h002081b3; // add  x3, x1, x2     # x3 = x1 + x2 = 3
            14'h002D: spo = 32'h0020f233; // and  x4, x1, x2     # x4 = x1 & x2 = 0
            14'h002E: spo = 32'h00209293; // slli x5, x1, 2      # x5 = x1 << 2 = 4
            14'h002F: spo = 32'h0020a313; // slt  x6, x1, x2     # x6 = (x1 < x2) = 1
            
            // 无限循环 (用于测试)
            14'h0030: spo = 32'h00108093; // addi x1, x1, 1      # x1++
            14'h0031: spo = 32'hffdff06f; // jal  x0, -4         # 跳转到上一条指令
            
            default:  spo = 32'h00000013; // NOP instruction (addi x0, x0, 0)
        endcase
    end

endmodule
