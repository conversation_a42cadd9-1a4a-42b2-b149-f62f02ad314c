`timescale 1ns / 1ps
`include "defines.vh"
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date: 2021/07/02 15:34:04
// Design Name: 
// Module Name: alu
// Project Name: 
// Target Devices: 
// Tool Versions: 
// Description: 
// ����������Ԫ���߼����㵥Ԫ���Ƚϵ�Ԫ����λ��Ԫ���Լ�ר����b��ָ������㵥Ԫ
// alu_opcode�ġ�3��2������ѡ��ͬ��Ԫ����������Ϊ�������
// alu_opcode�ġ�1��0����Ϊ��ͬ��Ԫ�Ĳ�����
// alu_opcode��4��ר������luiָ��ֱ�����op_b
// Dependencies: 
// arithmetic_unit, compare_unit,logic_unit,shift_unit,branch_unit
// Revision:
// Revision 0.01 - File Created
// Additional Comments:
// 
//////////////////////////////////////////////////////////////////////////////////
module alu #(
    parameter ARITHMETiC   = 2'b00,
    parameter COMPARE      = 2'b01,
    parameter LOGIC        = 2'b10,
    parameter SHIFT        = 2'b11
)
(
    input      [4:0]  alu_opcode_i,
    input      [31:0] alu_op_a_i,
    input      [31:0] alu_op_b_i,
    output reg [31:0] alu_result_o,
    output            branch_o
);
    wire [31:0] result_arithmetic;
    wire [31:0] result_compare;
    wire [31:0] result_logic;
    wire [31:0] result_shift;    
    // �������㵥Ԫ
    arithmetic_unit u_arithmetic_unit(
        .op_a_i  (alu_op_a_i),
        .op_b_i  (alu_op_b_i),
        .opcode_i(alu_opcode_i[1:0]),
        .result_o(result_arithmetic)
    );
    // �Ƚϲ�����Ԫ
    compare_unit u_compare_unit(
    .op_a_i  (alu_op_a_i),
    .op_b_i  (alu_op_b_i),
    .opcode_i(alu_opcode_i[1:0]),
    .result_o(result_compare)
    );
    // �߼����㵥Ԫ
    logic_unit u_logic_unit(
    .op_a_i  (alu_op_a_i),
    .op_b_i  (alu_op_b_i),
    .opcode_i(alu_opcode_i[1:0]),
    .result_o(result_logic)
    );
    // ��λ���㵥Ԫ
    shift_unit u_shift_unit(
    .op_a_i  (alu_op_a_i),
    .op_b_i  (alu_op_b_i),
    .opcode_i(alu_opcode_i[1:0]),
    .result_o(result_shift)
    );
    // B��ָ��ר��branch��֧���㵥Ԫ
    branch_unit u_branch_unit(
        .op_a_i  (alu_op_a_i),
        .op_b_i  (alu_op_b_i),
        .opcode_i(alu_opcode_i[3:0]),
        .branch  (branch_o)
    );
    // ����opcodeѡ������ĸ���Ԫ�Ľ��
    always @(*) begin
        // luiָ��
        if(alu_opcode_i[4])
            alu_result_o = alu_op_b_i;
        else
            case(alu_opcode_i[3:2])
                ARITHMETiC: alu_result_o = result_arithmetic;
                COMPARE:    alu_result_o = result_compare;
                LOGIC:      alu_result_o = result_logic;
                SHIFT:      alu_result_o = result_shift;
            endcase
    end

endmodule