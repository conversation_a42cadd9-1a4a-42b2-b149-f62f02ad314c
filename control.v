`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date: 2021/07/04 09:12:29
// Design Name: 
// Module Name: controler
// Project Name: 
// Target Devices: 
// Tool Versions: 
// Description: 
// �����������ս��������͵�opcode��function3��function7�ֶ�
// ������ֿ����ź�
// Dependencies: 
// 
// Revision:
// Revision 0.01 - File Created
// Additional Comments:
// 
//////////////////////////////////////////////////////////////////////////////////


module controler #(
    parameter OPCODE_JAL   = 7'b1101111,  // JAL指令
    parameter OPCODE_JALR  = 7'b1100111,  // J<PERSON>R指令
    parameter OPCODE_R     = 7'b0110011,  // R型指令 (ADD, SUB, SLL, SRL, SRA, AND, OR, XOR)
    parameter OPCODE_I     = 7'b0010011,  // I型指令 (ADDI, SLLI, SRLI, SRAI, ANDI, ORI, XORI)
    parameter OPCODE_AUIPC = 7'b0010111,  // AUIPC指令
    parameter OPCODE_LUI   = 7'b0110111   // LUI指令

)
(
    input             reset_i,
    input      [6:0]  opcode_i,
    input      [6:0]  function7_i,
    input      [2:0]  function3_i,
    output reg [1:0]  wd_sel_o,      // Regfileд������ѡ���ź�
    output reg [1:0]  pc_sel_o,      // npcѡ���ź�
    output reg        branch_o,      // B��ָ��������ת�ź� 
    output reg [2:0]  imm_sel_o,     // ������ѡ���ź�     
    output reg        regfile_we_o,  // Regfileд�����ź�
    output reg        mem_we_o,      // Memд�����ź�
    output reg        op_A_sel_o,    // ALU����ѡ���ź�
    output reg        op_B_sel_o,    // ALU����ѡ���ź�   
    output reg [4:0]  alu_opcode_o,  // alu������
    output reg [1:0]  mem_data_sel_o // Mem����ѡ���źţ����ڴ���sb,sh��lb,lh
    );
    // wd_sel��ֵ����
    always @(*) begin
        if(reset_i == 1'b1)
            wd_sel_o = 2'b11;
        else begin
            case(opcode_i)
                // jal,jalr - 写回PC+4
                OPCODE_JAL,OPCODE_JALR:
                    wd_sel_o = 2'b00;
                // R型和I型指令 - 写回ALU结果
                OPCODE_R, OPCODE_I, OPCODE_LUI, OPCODE_AUIPC:
                    wd_sel_o = 2'b01;
                default:
                    wd_sel_o = 2'b01;
            endcase
        end
    end

    // pc_sel��ֵ����
    always @(*) begin
        if(reset_i == 1'b1)
            pc_sel_o = 2'b00;
        else begin
            case(opcode_i)
                // jalr
                OPCODE_JALR:
                    pc_sel_o = 2'b11;
                // jal
                OPCODE_JAL:
                    pc_sel_o = 2'b10;
                // 其他指令 - PC+4
                default:
                    pc_sel_o = 2'b00;
            endcase
        end
    end

    // branch赋值逻辑 - 必做指令不包含分支指令
    always @(*) begin
        branch_o = 1'b0;  // 必做指令都不需要分支
    end

    // imm_sel��ֵ����
    always @(*) begin
        if(reset_i == 1'b1)
            imm_sel_o = 3'b000;
        else begin
            case(opcode_i)
                // R型指令 - 不需要立即数
                OPCODE_R:
                    imm_sel_o = 3'b000;
                // I型指令 - 区分移位和非移位指令
                OPCODE_I: begin
                    case(function3_i)
                        // 移位指令 (SLLI, SRLI, SRAI)
                        3'b001, 3'b101:
                            imm_sel_o = 3'b010;
                        // 其他I型指令 (ADDI, ANDI, ORI, XORI)
                        default:
                            imm_sel_o = 3'b001;
                    endcase
                end
                // JALR指令
                OPCODE_JALR:
                    imm_sel_o = 3'b001;
                // U型指令 (LUI, AUIPC)
                OPCODE_AUIPC, OPCODE_LUI:
                    imm_sel_o = 3'b101;
                // J型指令 (JAL)
                OPCODE_JAL:
                    imm_sel_o = 3'b110;
                default:
                    imm_sel_o = 3'b000;
            endcase
        end
    end

    // regfile_we_o����
    always @(*) begin
        if(reset_i == 1'b1)
            regfile_we_o = 1'b0;
        else begin
            case(opcode_i)
                // 必做指令都需要写寄存器
                OPCODE_R,      // R型指令 (ADD, SUB, SLL, SRL, SRA, AND, OR, XOR)
                OPCODE_I,      // I型指令 (ADDI, SLLI, SRLI, SRAI, ANDI, ORI, XORI)
                OPCODE_JAL,    // JAL指令
                OPCODE_LUI,    // LUI指令
                OPCODE_AUIPC,  // AUIPC指令
                OPCODE_JALR:   // JALR指令
                    regfile_we_o = 1'b1;
                default:
                    regfile_we_o = 1'b0;
            endcase
        end
    end

    // mem_we_o逻辑 - 必做指令不包含存储指令
    always @(*) begin
        mem_we_o = 1'b0;  // 必做指令都不需要写内存
    end

    // op_A_sel_o逻辑
    always @(*) begin
        case(opcode_i)
            // 使用寄存器rs1作为操作数A的指令
            OPCODE_R,      // R型指令
            OPCODE_I,      // I型指令
            OPCODE_JAL:    // JAL指令 (计算跳转地址)
                op_A_sel_o = 1'b1;  // 选择rs1
            // 使用PC作为操作数A的指令
            OPCODE_AUIPC:  // AUIPC指令
                op_A_sel_o = 1'b0;  // 选择PC
            default:
                op_A_sel_o = 1'b1;  // 默认选择rs1
        endcase
    end

    // op_B_sel_o逻辑
    always @(*) begin
        case(opcode_i)
            // 使用寄存器rs2作为操作数B的指令
            OPCODE_R:      // R型指令
                op_B_sel_o = 1'b1;  // 选择rs2
            // 使用立即数作为操作数B的指令
            OPCODE_I,      // I型指令
            OPCODE_JAL,    // JAL指令
            OPCODE_JALR,   // JALR指令
            OPCODE_LUI,    // LUI指令
            OPCODE_AUIPC:  // AUIPC指令
                op_B_sel_o = 1'b0;  // 选择立即数
            default:
                op_B_sel_o = 1'b0;  // 默认选择立即数
        endcase
    end
    
    // alu_opcode_o逻辑 - 只支持必做指令
    always @(*) begin
        case(opcode_i)
            // R型和I型指令
            OPCODE_R,
            OPCODE_I: begin
                case(function3_i)
                    // 算术运算：加法/减法
                    3'b000: begin
                        // I型指令只有加法 (ADDI)
                        if(opcode_i == OPCODE_I)
                            alu_opcode_o = 5'b00000;  // ADD
                        // R型指令根据funct7判断加法/减法
                        else
                            alu_opcode_o = (function7_i[5]==1'b0)?5'b00000:5'b00001;  // ADD/SUB
                    end
                    // 逻辑运算
                    3'b111:  // AND/ANDI
                        alu_opcode_o = 5'b01000;
                    3'b110:  // OR/ORI
                        alu_opcode_o = 5'b01001;
                    3'b100:  // XOR/XORI
                        alu_opcode_o = 5'b01010;
                    // 移位运算
                    3'b001:  // SLL/SLLI (左移)
                        alu_opcode_o = 5'b01100;
                    3'b101:  // SRL/SRLI/SRA/SRAI (右移)
                        alu_opcode_o = (function7_i[5]==1'b0)?5'b01101:5'b01110;  // 逻辑右移/算术右移
                    // 比较运算 (虽然不是必做，但ALU需要支持)
                    3'b010:  // SLT/SLTI
                        alu_opcode_o = 5'b00100;
                    3'b011:  // SLTU/SLTIU
                        alu_opcode_o = 5'b00101;
                    default:
                        alu_opcode_o = 5'b00000;
                    endcase
            end
            // JAL指令 - 计算跳转地址 (PC + offset)
            OPCODE_JAL:
                alu_opcode_o = 5'b00000;  // ADD
            // JALR指令 - 计算跳转地址 (rs1 + offset)
            OPCODE_JALR:
                alu_opcode_o = 5'b00000;  // ADD
            // LUI指令 - 直接输出立即数
            OPCODE_LUI:
                alu_opcode_o = 5'b10000;  // 特殊操作码，直接输出操作数B
            // AUIPC指令 - 计算PC + offset
            OPCODE_AUIPC:
                alu_opcode_o = 5'b00000;  // ADD
            default:
                alu_opcode_o = 5'b00000;
        endcase
    end

    // mem_data_sel逻辑 - 必做指令不需要存储器访问
    always@(*) begin
        mem_data_sel_o = 2'b00;  // 默认值，不使用
    end
endmodule