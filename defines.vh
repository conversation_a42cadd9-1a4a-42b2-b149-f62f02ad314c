// Annotate this macro before synthesis
// `define RUN_TRACE

// 单周期RISC-V CPU设计 - 支持21条必做指令
// 必做指令包括：
// - 算术运算：ADD, ADDI, SUB, LUI, AUIPC (5条)
// - 逻辑运算：AND, ANDI, OR, ORI, XOR, XORI (6条)
// - 移位运算：SLL, SLLI, SRL, SRLI, SRA, SRAI (6条)
// - 跳转链接：JAL, JALR (2条)
// - 比较指令：SLT, SLTI (2条)

// ALU操作码定义 (21条指令对应的ALU操作)
`define ALU_OP_ADD    5'b00000   // 加法 (ADD, ADDI, AUIPC, JAL, JALR)
`define ALU_OP_SUB    5'b00001   // 减法 (SUB)
`define ALU_OP_AND    5'b01000   // 逻辑与 (AND, ANDI)
`define ALU_OP_OR     5'b01001   // 逻辑或 (OR, ORI)
`define ALU_OP_XOR    5'b01010   // 逻辑异或 (XOR, XORI)
`define ALU_OP_SLL    5'b01100   // 逻辑左移 (SLL, SLLI)
`define ALU_OP_SRL    5'b01101   // 逻辑右移 (SRL, SRLI)
`define ALU_OP_SRA    5'b01110   // 算术右移 (SRA, SRAI)
`define ALU_OP_SLT    5'b00100   // 有符号比较 (SLT, SLTI)
`define ALU_OP_LUI    5'b10000   // 上位立即数 (LUI)

// 立即数类型选择
`define IMM_TYPE_I    3'b001     // I型立即数 (12位符号扩展)
`define IMM_TYPE_S    3'b011     // S型立即数 (12位符号扩展)
`define IMM_TYPE_B    3'b100     // B型立即数 (13位符号扩展)
`define IMM_TYPE_U    3'b101     // U型立即数 (20位左移12位)
`define IMM_TYPE_J    3'b110     // J型立即数 (21位符号扩展)
`define IMM_TYPE_SHAMT 3'b010    // 移位量 (5位零扩展)

// 写回数据选择
`define WD_SEL_PC4    2'b00      // 写回PC+4 (JAL, JALR)
`define WD_SEL_ALU    2'b01      // 写回ALU结果 (算术、逻辑、移位、比较指令)
`define WD_SEL_MEM    2'b10      // 写回存储器数据 (加载指令，不在必做范围)

// PC选择
`define PC_SEL_PC4    2'b00      // PC = PC + 4 (顺序执行)
`define PC_SEL_BR     2'b01      // PC = PC + offset (分支指令，不在必做范围)
`define PC_SEL_JAL    2'b10      // PC = PC + offset (JAL)
`define PC_SEL_JALR   2'b11      // PC = rs1 + offset (JALR)

// ALU操作数A选择
`define OP_A_SEL_RS1  1'b1       // 选择rs1
`define OP_A_SEL_PC   1'b0       // 选择PC

// ALU操作数B选择
`define OP_B_SEL_RS2  1'b1       // 选择rs2
`define OP_B_SEL_IMM  1'b0       // 选择立即数

// 外设I/O接口电路的端口地址 (保留用于显示调试)
`define PERI_ADDR_DIG   32'hFFFF_F000
`define PERI_ADDR_LED   32'hFFFF_F060
`define PERI_ADDR_SW    32'hFFFF_F070
`define PERI_ADDR_BTN   32'hFFFF_F078
