// Annotate this macro before synthesis
// `define RUN_TRACE

// 单周期RISC-V CPU设计 - 只支持必做指令
// 必做指令包括：
// - 算术运算：ADD, ADDI, SUB, LUI, AUIPC
// - 逻辑运算：AND, ANDI, OR, ORI, XOR, XORI
// - 移位运算：SLL, SLLI, SRL, SRLI, SRA, SRAI
// - 跳转链接：JAL, JALR

// 外设I/O接口电路的端口地址 (保留用于显示调试)
`define PERI_ADDR_DIG   32'hFFFF_F000
`define PERI_ADDR_LED   32'hFFFF_F060
`define PERI_ADDR_SW    32'hFFFF_F070
`define PERI_ADDR_BTN   32'hFFFF_F078
