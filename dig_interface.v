`timescale 1ns / 1ps

module dig_interface (
    input  wire         rst,
    input  wire         clk,
    input  wire [31:0]  addr,
    input  wire         we,
    input  wire [31:0]  wdata,
    
    output wire [ 7:0]  dig_en,
    output wire         DN_A,
    output wire         DN_B,
    output wire         DN_C,
    output wire         DN_D,
    output wire         DN_E,
    output wire         DN_F,
    output wire         DN_G,
    output wire         DN_DP
);

    reg [31:0] dig_data;
    
    // Write data register
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            dig_data <= 32'h0;
        end else if (we) begin
            dig_data <= wdata;
        end
    end
    
    // Instantiate display module
    display u_display (
        .clk        (clk),
        .rst_n      (~rst),
        .busy       (1'b0),
        .z1         (dig_data[31:24]),
        .r1         (dig_data[23:16]),
        .z2         (dig_data[15:8]),
        .r2         (dig_data[7:0]),
        .led0_en    (dig_en[0]),
        .led1_en    (dig_en[1]),
        .led2_en    (dig_en[2]),
        .led3_en    (dig_en[3]),
        .led4_en    (dig_en[4]),
        .led5_en    (dig_en[5]),
        .led6_en    (dig_en[6]),
        .led7_en    (dig_en[7]),
        .led_ca     (DN_A),
        .led_cb     (DN_B),
        .led_cc     (DN_C),
        .led_cd     (DN_D),
        .led_ce     (DN_E),
        .led_cf     (DN_F),
        .led_cg     (DN_G),
        .led_dp     (DN_DP)
    );

endmodule
