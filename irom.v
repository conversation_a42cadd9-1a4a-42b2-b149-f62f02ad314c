`timescale 1ns / 1ps

// Behavioral model for IROM IP core
// This is a simple instruction ROM for testing purposes
module IROM (
    input  wire [13:0] a,    // Address input (14-bit for normal mode)
    output reg  [31:0] spo   // Data output
);

    // Simple instruction memory - just return NOP instructions for now
    // In a real implementation, this would be initialized with actual program
    always @(*) begin
        case (a)
            default: spo = 32'h00000013; // NOP instruction (addi x0, x0, 0)
        endcase
    end

endmodule

// Wrapper module for compatibility
module irom(
    input  [31:0] pc_i,
    output [31:0] inst_o
    );
    IROM U0_irom(
        .a     (pc_i[15:2]),
        .spo   (inst_o)
    );
endmodule