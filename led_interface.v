`timescale 1ns / 1ps

module led_interface (
    input  wire         rst,
    input  wire         clk,
    input  wire [31:0]  addr,
    input  wire         we,
    input  wire [31:0]  wdata,
    
    output reg  [23:0]  led
);

    // Write data register
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            led <= 24'h0;
        end else if (we) begin
            led <= wdata[23:0];
        end
    end

endmodule
