`timescale 1ns / 1ps

`include "defines.vh"

// 单周期RISC-V CPU SoC系统
// 支持必做指令：算术运算、逻辑运算、移位运算、跳转链接指令
module miniRV_SoC (
    input  wire         fpga_rst,   // 复位信号，高电平有效
    input  wire         fpga_clk,   // FPGA输入时钟

    // 外设接口
    input  wire [23:0]  sw,         // 开关输入
    input  wire [ 4:0]  button,     // 按键输入
    output wire [ 7:0]  dig_en,     // 数码管位选信号
    output wire         DN_A,       // 数码管段选信号A
    output wire         DN_B,       // 数码管段选信号B
    output wire         DN_C,       // 数码管段选信号C
    output wire         DN_D,       // 数码管段选信号D
    output wire         DN_E,       // 数码管段选信号E
    output wire         DN_F,       // 数码管段选信号F
    output wire         DN_G,       // 数码管段选信号G
    output wire         DN_DP,      // 数码管小数点信号
    output wire [23:0]  led         // LED输出

`ifdef RUN_TRACE
    ,// 调试接口
    output wire         debug_wb_have_inst, // 当前时钟周期是否有指令写回 (对单周期CPU，可在复位后恒置1)
    output wire [31:0]  debug_wb_pc,        // 当前写回的指令的PC (若wb_have_inst=0，此项可为任意值)
    output              debug_wb_ena,       // 指令写回时，寄存器堆的写使能 (若wb_have_inst=0，此项可为任意值)
    output wire [ 4:0]  debug_wb_reg,       // 指令写回时，写入的寄存器号 (若wb_ena或wb_have_inst=0，此项可为任意值)
    output wire [31:0]  debug_wb_value      // 指令写回时，写入寄存器的值 (若wb_ena或wb_have_inst=0，此项可为任意值)
`endif
);

    // 时钟和复位信号
    wire        pll_lock;               // PLL锁定信号
    wire        pll_clk;                // PLL输出时钟
    wire        cpu_clk;                // CPU工作时钟

    // CPU与指令存储器接口
`ifdef RUN_TRACE
    wire [15:0] inst_addr;              // 指令地址 (调试模式16位)
`else
    wire [13:0] inst_addr;              // 指令地址 (正常模式14位)
`endif
    wire [31:0] inst;                   // 指令数据

    // CPU与总线桥接器接口
    wire [31:0] Bus_rdata;              // 总线读数据
    wire [31:0] Bus_addr;               // 总线地址
    wire        Bus_we;                 // 总线写使能
    wire [31:0] Bus_wdata;              // 总线写数据

    // 桥接器与数据存储器接口
    // wire         rst_bridge2dram;      // 数据存储器复位信号 (未使用)
    wire         clk_bridge2dram;         // 数据存储器时钟信号
    wire [31:0]  addr_bridge2dram;        // 数据存储器地址
    wire [31:0]  rdata_dram2bridge;       // 数据存储器读数据
    wire         we_bridge2dram;          // 数据存储器写使能
    wire [31:0]  wdata_bridge2dram;       // 数据存储器写数据

    // 桥接器与外设接口
    // 桥接器与7段数码管接口
    wire         rst_bridge2dig;          // 数码管复位信号
    wire         clk_bridge2dig;          // 数码管时钟信号
    wire [31:0]  addr_bridge2dig;         // 数码管地址
    wire         we_bridge2dig;           // 数码管写使能
    wire [31:0]  wdata_bridge2dig;        // 数码管写数据

    // 桥接器与LED接口
    wire         rst_bridge2led;          // LED复位信号
    wire         clk_bridge2led;          // LED时钟信号
    wire [31:0]  addr_bridge2led;         // LED地址
    wire         we_bridge2led;           // LED写使能
    wire [31:0]  wdata_bridge2led;        // LED写数据

    // 桥接器与开关接口
    wire         rst_bridge2sw;           // 开关复位信号
    wire         clk_bridge2sw;           // 开关时钟信号
    wire [31:0]  addr_bridge2sw;          // 开关地址
    wire [31:0]  rdata_sw2bridge;         // 开关读数据

    // 桥接器与按键接口
    wire         rst_bridge2btn;          // 按键复位信号
    wire         clk_bridge2btn;          // 按键时钟信号
    wire [31:0]  addr_bridge2btn;         // 按键地址
    wire [31:0]  rdata_btn2bridge;        // 按键读数据
    

    // 时钟管理
`ifdef RUN_TRACE
    // Trace调试时，直接使用外部输入时钟
    assign cpu_clk = fpga_clk;
`else
    // 下板时，使用PLL分频后的时钟
    assign cpu_clk = pll_clk & pll_lock;
    cpuclk Clkgen (
        // .resetn     (!fpga_rst),        // 复位信号 (未使用)
        .clk_in1    (fpga_clk),           // 输入时钟
        .clk_out1   (pll_clk),            // 输出时钟
        .locked     (pll_lock)            // PLL锁定信号
    );
`endif

    // CPU核心实例化
    myCPU Core_cpu (
        .cpu_rst            (fpga_rst),   // CPU复位信号
        .cpu_clk            (cpu_clk),    // CPU时钟信号

        // 与指令存储器接口
        .inst_addr          (inst_addr),  // 指令地址
        .inst               (inst),       // 指令数据

        // 与总线桥接器接口
        .Bus_addr           (Bus_addr),   // 总线地址
        .Bus_rdata          (Bus_rdata),  // 总线读数据
        .Bus_we             (Bus_we),     // 总线写使能
        .Bus_wdata          (Bus_wdata)   // 总线写数据

`ifdef RUN_TRACE
        ,// 调试接口
        .debug_wb_have_inst (debug_wb_have_inst), // 写回指令有效信号
        .debug_wb_pc        (debug_wb_pc),        // 写回指令PC
        .debug_wb_ena       (debug_wb_ena),       // 寄存器写使能
        .debug_wb_reg       (debug_wb_reg),       // 写回寄存器号
        .debug_wb_value     (debug_wb_value)      // 写回寄存器值
`endif
    );

    // 指令存储器实例化
    IROM Mem_IROM (
        .a          (inst_addr),          // 指令地址输入
        .spo        (inst)                // 指令数据输出
    );

    // 总线桥接器实例化
    Bridge Bridge (
        // 与CPU接口
        .rst_from_cpu       (fpga_rst),   // 来自CPU的复位信号
        .clk_from_cpu       (cpu_clk),    // 来自CPU的时钟信号
        .addr_from_cpu      (Bus_addr),   // 来自CPU的地址
        .we_from_cpu        (Bus_we),     // 来自CPU的写使能
        .wdata_from_cpu     (Bus_wdata),  // 来自CPU的写数据
        .rdata_to_cpu       (Bus_rdata),  // 发送给CPU的读数据

        // 与数据存储器接口
        // .rst_to_dram    (rst_bridge2dram),     // 发送给DRAM的复位信号 (未使用)
        .clk_to_dram        (clk_bridge2dram),   // 发送给DRAM的时钟信号
        .addr_to_dram       (addr_bridge2dram),  // 发送给DRAM的地址
        .rdata_from_dram    (rdata_dram2bridge), // 来自DRAM的读数据
        .we_to_dram         (we_bridge2dram),    // 发送给DRAM的写使能
        .wdata_to_dram      (wdata_bridge2dram), // 发送给DRAM的写数据

        // 与7段数码管接口
        .rst_to_dig         (rst_bridge2dig),    // 发送给数码管的复位信号
        .clk_to_dig         (clk_bridge2dig),    // 发送给数码管的时钟信号
        .addr_to_dig        (addr_bridge2dig),   // 发送给数码管的地址
        .we_to_dig          (we_bridge2dig),     // 发送给数码管的写使能
        .wdata_to_dig       (wdata_bridge2dig),  // 发送给数码管的写数据

        // 与LED接口
        .rst_to_led         (rst_bridge2led),    // 发送给LED的复位信号
        .clk_to_led         (clk_bridge2led),    // 发送给LED的时钟信号
        .addr_to_led        (addr_bridge2led),   // 发送给LED的地址
        .we_to_led          (we_bridge2led),     // 发送给LED的写使能
        .wdata_to_led       (wdata_bridge2led),  // 发送给LED的写数据

        // 与开关接口
        .rst_to_sw          (rst_bridge2sw),     // 发送给开关的复位信号
        .clk_to_sw          (clk_bridge2sw),     // 发送给开关的时钟信号
        .addr_to_sw         (addr_bridge2sw),    // 发送给开关的地址
        .rdata_from_sw      (rdata_sw2bridge),   // 来自开关的读数据

        // 与按键接口
        .rst_to_btn         (rst_bridge2btn),    // 发送给按键的复位信号
        .clk_to_btn         (clk_bridge2btn),    // 发送给按键的时钟信号
        .addr_to_btn        (addr_bridge2btn),   // 发送给按键的地址
        .rdata_from_btn     (rdata_btn2bridge)   // 来自按键的读数据
    );

    DRAM Mem_DRAM (
        .clk        (clk_bridge2dram),
        .a          (addr_bridge2dram[15:2]),
        .spo        (rdata_dram2bridge),
        .we         (we_bridge2dram),
        .d          (wdata_bridge2dram)
    );
    
    // Instantiate peripheral I/O interface modules
    dig_interface Peri_DIG (
        .rst        (rst_bridge2dig),
        .clk        (clk_bridge2dig),
        .addr       (addr_bridge2dig),
        .we         (we_bridge2dig),
        .wdata      (wdata_bridge2dig),
        .dig_en     (dig_en),
        .DN_A       (DN_A),
        .DN_B       (DN_B),
        .DN_C       (DN_C),
        .DN_D       (DN_D),
        .DN_E       (DN_E),
        .DN_F       (DN_F),
        .DN_G       (DN_G),
        .DN_DP      (DN_DP)
    );

    led_interface Peri_LED (
        .rst        (rst_bridge2led),
        .clk        (clk_bridge2led),
        .addr       (addr_bridge2led),
        .we         (we_bridge2led),
        .wdata      (wdata_bridge2led),
        .led        (led)
    );

    sw_interface Peri_SW (
        .rst        (rst_bridge2sw),
        .clk        (clk_bridge2sw),
        .addr       (addr_bridge2sw),
        .sw         (sw),
        .rdata      (rdata_sw2bridge)
    );

    btn_interface Peri_BTN (
        .rst        (rst_bridge2btn),
        .clk        (clk_bridge2btn),
        .addr       (addr_bridge2btn),
        .button     (button),
        .rdata      (rdata_btn2bridge)
    );


endmodule
