`timescale 1ns / 1ps

`include "defines.vh"
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date: 2021/07/02 17:18:09
// Design Name: 
// Module Name: my_cpu
// Project Name: 
// Target Devices: 
// Tool Versions: 
// Description: 
// cpuģ�飬����ȡָ�����롢�ô桢ִ�С����������ģ�飬����������ߵ�����
// Dependencies: 
// controler��instruction_fetch,instruction_deocder,execute,mem
// Revision:
// Revision 0.01 - File Created
// Additional Comments:
// 
//////////////////////////////////////////////////////////////////////////////////
module myCPU(
    input         cpu_rst,            // CPU reset
    input         cpu_clk,            // CPU clock

    // Interface to IROM
`ifdef RUN_TRACE
    output [15:0] inst_addr,
`else
    output [13:0] inst_addr,
`endif
    input  [31:0] inst,

    // Interface to Bridge
    output [31:0] Bus_addr,
    input  [31:0] Bus_rdata,
    output        Bus_we,
    output [31:0] Bus_wdata

`ifdef RUN_TRACE
    ,// Debug Interface
    output        debug_wb_have_inst, // 当前时钟周期是否有指令写回 (对单周期CPU，可在复位后恒置1)
    output [31:0] debug_wb_pc,        // 当前写回的指令的PC (若wb_have_inst=0，此项可为任意值)
    output        debug_wb_ena,       // 指令写回时，寄存器堆的写使能 (若wb_have_inst=0，此项可为任意值)
    output [4:0]  debug_wb_reg,       // 指令写回时，写入的寄存器号 (若wb_ena或wb_have_inst=0，此项可为任意值)
    output [31:0] debug_wb_value      // 指令写回时，写入寄存器的值 (若wb_ena或wb_have_inst=0，此项可为任意值)
`endif
    );
    // �����ź�

    wire [31:0] ext;          // ������չ������������
    wire [31:0] rD1;          // Regfile������reg1
    wire [31:0] rD2;          // Regfile������reg2
    wire [31:0] return_pc;    // ����jal��jalr����pc+4�洢��rd
    wire [31:0] current_pc;   // ����B��ָ�����ǰpcֵ����ALU

    wire [31:0] alu_result;   // alu���������
    wire [31:0] mem_rd;       // dmem��������
    // �����ź�
    wire [1:0] wd_sel;        // ѡ��д��regfile�����ݿ�����
    wire [1:0] pc_sel;        // pcѡ�������
    wire branch_controler;    // branch������
    wire branch_exe;          // alu������branch
    wire branch;
    wire [2:0] imm_sel;             // ����������չ��Ԫ�Ŀ����ߣ�ѡ������ʵ�������
    wire regfile_we;          // Regfileдʹ�ܿ���
    wire mem_we;              // dmemдʹ�ܿ���
    wire op_A_sel;            // ALU����ѡ���ź�
    wire op_B_sel;            // ALU����ѡ���ź�
    wire [4:0] alu_opcode;    // ALU������
    // test
    reg [31:0] pc_tmp;
    wire [1:0] mem_data_sel;
    
    
    // Interface assignments
    assign Bus_addr = alu_result;
    assign Bus_we = mem_we;
    assign Bus_wdata = rD2;
    assign branch = branch_controler & branch_exe;
`ifdef RUN_TRACE
    assign inst_addr = current_pc[15:2];  // PC to instruction address (16-bit for trace)
`else
    assign inst_addr = current_pc[15:2];  // PC to instruction address (14-bit for normal)
`endif

`ifdef RUN_TRACE
    // Debug interface assignments
    assign debug_wb_have_inst = 1'b1;  // Always have instruction for single cycle
    assign debug_wb_pc = current_pc;
    assign debug_wb_ena = regfile_we;
    assign debug_wb_reg = inst[11:7];   // rd field
    assign debug_wb_value = (wd_sel == 2'b00) ? return_pc :
                           (wd_sel == 2'b01) ? alu_result : mem_rd;
`endif

    // cpuclk u_cpuclk(
    //     .clk_in1    (sysclk_i),
    //     .clk_out1   (cpu_clk)
    // );

    controler u_controler(
        .reset_i     (cpu_rst),
        .opcode_i    (inst[6:0]),
        .function7_i (inst[31:25]),
        .function3_i (inst[14:12]),
        .wd_sel_o    (wd_sel),
        .pc_sel_o    (pc_sel),
        .branch_o    (branch_controler),
        .imm_sel_o   (imm_sel),
        .regfile_we_o(regfile_we),
        .mem_we_o    (mem_we),
        .op_A_sel_o  (op_A_sel),
        .op_B_sel_o  (op_B_sel),
        .alu_opcode_o(alu_opcode),
        .mem_data_sel_o(mem_data_sel)
    );

    instruction_fetch u_instruction_fetch(
        .clk_i       (cpu_clk),
        .reset_i     (cpu_rst),
        .pc_sel_i    (pc_sel),
        .offset_i    (ext),
        .rD1_i       (rD1),
        .branch_i    (branch),
        .inst_o      (),  // Not used, inst comes from external IROM
        .return_pc_o (return_pc),
        .current_pc_o(current_pc),
        .pc_o        ()   // Not used, PC address goes to inst_addr
    );
    instruction_decoder u_instruction_decoder(
        .clk_i       (cpu_clk),
        .reset_i     (cpu_rst),
        .return_pc_i (return_pc),
        .ALU_result_i(alu_result),
        .mem_data_i  (mem_rd),
        .wd_sel_i    (wd_sel),
        .we_i        (regfile_we),
        .inst_i      (inst),
        .imm_sel_i   (imm_sel),
        .ext_o       (ext),
        .rD1_o       (rD1),
        .rD2_o       (rD2),
        .we_o        (),  // Not used in this interface
        .wD_o        (),  // Not used in this interface
        .wR_o        (),  // Not used in this interface
        .rD19_o      ()   // Not used in this interface
    );
    execute u_execute(
        .op_A_sel_i  (op_A_sel),
        .op_B_sel_i  (op_B_sel),
        .current_pc_i(current_pc),
        .rD1_i       (rD1),
        .rD2_i       (rD2),
        .ext_i       (ext),
        .alu_result_o(alu_result),
        .alu_opcode_i(alu_opcode),
        .alu_branch_o    (branch_exe)
    );
    // Memory data comes from Bus_rdata for loads
    assign mem_rd = Bus_rdata;
endmodule