`timescale 1ns / 1ps

// Simple testbench for mandatory RISC-V instructions
module testbench_simple;

    // Clock and reset
    reg fpga_clk;
    reg fpga_rst;
    
    // External I/O (not used in mandatory instructions)
    reg [23:0] sw;
    reg [4:0] button;
    wire [7:0] dig_en;
    wire DN_A, DN_B, DN_C, DN_D, DN_E, DN_F, DN_G, DN_DP;
    wire [23:0] led;

    // Instantiate the SoC
    miniRV_SoC uut (
        .fpga_rst(fpga_rst),
        .fpga_clk(fpga_clk),
        .sw(sw),
        .button(button),
        .dig_en(dig_en),
        .DN_A(DN_A), .DN_B(DN_B), .DN_C(DN_C), .DN_D(DN_D),
        .DN_E(DN_E), .DN_F(DN_F), .DN_G(DN_G), .DN_DP(DN_DP),
        .led(led)
    );

    // Clock generation
    initial begin
        fpga_clk = 0;
        forever #5 fpga_clk = ~fpga_clk; // 100MHz clock
    end

    // Test sequence
    initial begin
        // Initialize
        fpga_rst = 1;
        sw = 24'h0;
        button = 5'h0;
        
        // Wait for reset
        #100;
        fpga_rst = 0;
        
        // Let the CPU run for some cycles
        #1000;
        
        // Display some register values (if accessible)
        $display("Test completed at time %t", $time);
        $display("LED output: %h", led);
        
        $finish;
    end

    // Monitor important signals
    initial begin
        $monitor("Time: %t, PC: %h, Instruction: %h, Reset: %b", 
                 $time, uut.Core_cpu.current_pc, uut.inst, fpga_rst);
    end

endmodule
